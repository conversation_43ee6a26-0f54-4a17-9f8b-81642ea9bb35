﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Google.Api.CommonProtos" version="2.5.0" targetFramework="net472" />
  <package id="Google.Api.Gax" version="3.7.0" targetFramework="net472" />
  <package id="Google.Api.Gax.Grpc" version="3.7.0" targetFramework="net472" />
  <package id="Google.Api.Gax.Grpc.GrpcCore" version="3.7.0" targetFramework="net472" />
  <package id="Google.Apis" version="1.55.0" targetFramework="net472" />
  <package id="Google.Apis.Auth" version="1.55.0" targetFramework="net472" />
  <package id="Google.Apis.Core" version="1.55.0" targetFramework="net472" />
  <package id="Google.Cloud.TextToSpeech.V1" version="2.5.0" targetFramework="net472" />
  <package id="Google.Protobuf" version="3.18.0" targetFramework="net472" />
  <package id="Grpc.Auth" version="2.41.0" targetFramework="net472" />
  <package id="Grpc.Core" version="2.41.0" targetFramework="net472" />
  <package id="Grpc.Core.Api" version="2.41.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.0.0" targetFramework="net472" />
  <package id="Microsoft.Win32.Registry" version="4.7.0" targetFramework="net472" />
  <package id="Microsoft.WindowsAPICodePack-Core" version="1.1.0.0" targetFramework="net472" />
  <package id="Microsoft.WindowsAPICodePack-Shell" version="1.1.0.0" targetFramework="net472" />
  <package id="NAudio" version="2.1.0" targetFramework="net472" />
  <package id="NAudio.Asio" version="2.1.0" targetFramework="net472" />
  <package id="NAudio.Core" version="2.1.0" targetFramework="net472" />
  <package id="NAudio.Midi" version="2.1.0" targetFramework="net472" />
  <package id="NAudio.Wasapi" version="2.1.0" targetFramework="net472" />
  <package id="NAudio.WinForms" version="2.1.0" targetFramework="net472" />
  <package id="NAudio.WinMM" version="2.1.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.3" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.2" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="4.7.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.2" targetFramework="net472" />
</packages>