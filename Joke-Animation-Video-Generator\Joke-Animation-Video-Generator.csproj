﻿<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <RootNamespace>Joke_Animation_Video_Generator</RootNamespace>
    <UseWindowsForms>true</UseWindowsForms>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="bin\Debug\netcoreapp3.1\description\description.txt" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Google.Apis.YouTube.v3" Version="1.57.0.2721" />
    <PackageReference Include="Google.Cloud.TextToSpeech.V1" Version="3.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="5.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="5.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="5.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.WindowsAPICodePack-Core" Version="1.1.0" />
    <PackageReference Include="Microsoft.WindowsAPICodePack-Shell" Version="1.1.0" />
    <PackageReference Include="NAudio" Version="2.1.0" />
    <PackageReference Include="NReco.VideoInfo" Version="1.1.1" />
    <PackageReference Include="System.Speech" Version="6.0.0" />
  </ItemGroup>

</Project>