﻿// <auto-generated />
using Joke_Video_Generator;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Joke_Video_Generator.Migrations
{
    [DbContext(typeof(Context))]
    [Migration("20220605124020_init")]
    partial class init
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "5.0.0");

            modelBuilder.Entity("Joke_Video_Generator.Joke", b =>
                {
                    b.Property<string>("id")
                        .HasColumnType("TEXT");

                    b.Property<string>("body")
                        .HasColumnType("TEXT");

                    b.Property<int>("score")
                        .HasColumnType("INTEGER");

                    b.Property<string>("title")
                        .HasColumnType("TEXT");

                    b.<PERSON>("id");

                    b.ToTable("Joke");
                });
#pragma warning restore 612, 618
        }
    }
}
