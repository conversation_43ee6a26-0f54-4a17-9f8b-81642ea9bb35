﻿// <auto-generated />
using Joke_Video_Generator;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Joke_Video_Generator.Migrations
{
    [DbContext(typeof(Context))]
    partial class ContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "5.0.0");

            modelBuilder.Entity("Joke_Video_Generator.Joke", b =>
                {
                    b.Property<string>("id")
                        .HasColumnType("TEXT");

                    b.Property<string>("body")
                        .HasColumnType("TEXT");

                    b.Property<int>("no_of_charachter")
                        .HasColumnType("INTEGER");

                    b.Property<int>("score")
                        .HasColumnType("INTEGER");

                    b.Property<string>("title")
                        .HasColumnType("TEXT");

                    b.<PERSON>("id");

                    b.ToT<PERSON>("Joke");
                });
#pragma warning restore 612, 618
        }
    }
}
